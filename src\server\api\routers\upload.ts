import { z } from "zod";
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { R2 } from '@/lib/r2';
import crypto from 'crypto';
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { env } from "@/env";

// 生成一个随机的文件名
const generateFileName = (bytes = 16) => crypto.randomBytes(bytes).toString('hex');

export const uploadRouter = createTRPCRouter({
  upload: protectedProcedure
    .input(z.object({
        fileType: z.string().regex(/^image\/(jpeg|png|webp)$/, "Unsupported file type."),
        fileSize: z.number().max(10 * 1024 * 1024, "File size exceeds 10MB."),
      }))
    .mutation(async({ ctx, input }) => {
      const { fileType, fileSize } = input;
      const userId = ctx.session.user.id;

      const key = `${userId}/${generateFileName()}.${fileType.split('/')[1]}`;

      const command = new PutObjectCommand({
      Bucket: env.R2_BUCKET_NAME,
      Key: key,
      ContentType: fileType,
      ContentLength: fileSize,
    });

    const signedUrl = await getSignedUrl(R2, command, { expiresIn: 60 });
      
      return {signedUrl, key};
    }),
});