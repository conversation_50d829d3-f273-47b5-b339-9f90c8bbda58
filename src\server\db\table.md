# 数据库表结构文档

## 概述

本文档描述了 Flux-Pix 项目的数据库表结构。该项目使用 PostgreSQL 数据库和 Drizzle ORM，支持用户认证、订阅管理、资源存储和图像创作功能。

## 表结构

### 1. users (用户表)

用户基本信息表，存储用户账户数据。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | varchar | 255 | PRIMARY KEY, NOT NULL | UUID | 用户唯一标识符 |
| name | varchar | 255 | - | - | 用户姓名 |
| email | varchar | 255 | NOT NULL | - | 用户邮箱地址 |
| emailVerified | timestamp | - | - | CURRENT_TIMESTAMP | 邮箱验证时间 |
| image | varchar | 255 | - | - | 用户头像URL |

**关系：**
- 一对多：accounts (用户账户)

---

### 2. accounts (账户表)

存储用户的第三方登录账户信息（OAuth）。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| userId | varchar | 255 | NOT NULL, FK | - | 关联用户ID |
| type | varchar | 255 | NOT NULL | - | 账户类型 |
| provider | varchar | 255 | NOT NULL | - | 登录提供商 |
| providerAccountId | varchar | 255 | NOT NULL | - | 提供商账户ID |
| refresh_token | text | - | - | - | 刷新令牌 |
| access_token | text | - | - | - | 访问令牌 |
| expires_at | integer | - | - | - | 令牌过期时间 |
| token_type | varchar | 255 | - | - | 令牌类型 |
| scope | varchar | 255 | - | - | 权限范围 |
| id_token | text | - | - | - | ID令牌 |
| session_state | varchar | 255 | - | - | 会话状态 |

**主键：** provider + providerAccountId (复合主键)
**索引：** account_user_id_idx (userId)
**关系：**
- 多对一：users (用户)

---

### 3. sessions (会话表)

存储用户登录会话信息。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| sessionToken | varchar | 255 | PRIMARY KEY, NOT NULL | - | 会话令牌 |
| userId | varchar | 255 | NOT NULL, FK | - | 关联用户ID |
| expires | timestamp | - | NOT NULL | - | 会话过期时间 |

**索引：** session_user_id_idx (userId)
**关系：**
- 多对一：users (用户)

---

### 4. verificationTokens (验证令牌表)

存储邮箱验证等验证令牌。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| identifier | varchar | 255 | NOT NULL | - | 标识符（通常是邮箱） |
| token | varchar | 255 | NOT NULL | - | 验证令牌 |
| expires | timestamp | - | NOT NULL | - | 令牌过期时间 |

**主键：** identifier + token (复合主键)

---

### 5. subscriptionPlans (订阅计划表)

存储不同的订阅套餐信息。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | varchar | 255 | PRIMARY KEY, NOT NULL | UUID | 计划唯一标识符 |
| planName | varchar | 50 | NOT NULL, UNIQUE | - | 计划名称 |
| priceMonthly | numeric | 10,2 | NOT NULL | - | 月费价格 |
| creditsPerMonth | integer | - | NOT NULL | - | 每月积分数 |
| features | jsonb | - | - | - | 功能特性（JSON格式） |

---

### 6. assets (资源表)

存储用户上传的图片和生成的图片资源。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | varchar | 255 | PRIMARY KEY, NOT NULL | UUID | 资源唯一标识符 |
| userId | varchar | 255 | NOT NULL, FK | - | 资源所有者ID |
| url | text | - | NOT NULL, UNIQUE | - | 资源URL地址 |
| createdAt | timestamp | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**关系：**
- 多对一：users (上传者)

---

### 7. creations (创作表)

存储用户的图像编辑创作记录。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | varchar | 255 | PRIMARY KEY, NOT NULL | UUID | 创作唯一标识符 |
| userId | varchar | 255 | NOT NULL, FK | - | 创作者ID |
| prompt | text | - | - | - | 编辑提示词 |
| originalAssetId | varchar | 255 | NOT NULL, FK | - | 原始图片资源ID |
| resultAssetId | varchar | 255 | FK | - | 结果图片资源ID |
| aiModel | varchar | 100 | - | - | 使用的AI模型 |
| creditsConsumed | integer | - | - | - | 消耗的积分数 |
| createdAt | timestamp | - | NOT NULL | CURRENT_TIMESTAMP | 创作时间 |

**关系：**
- 多对一：users (创作者)
- 多对一：assets (原始资源)
- 多对一：assets (结果资源)

---

## 表关系图

```
users (1) ----< accounts (N)
users (1) ----< sessions (N)
users (1) ----< assets (N)
users (1) ----< creations (N)
assets (1) ----< creations (N) [originalAssetId]
assets (1) ----< creations (N) [resultAssetId]
```

## 数据库配置

- **数据库类型：** PostgreSQL
- **ORM：** Drizzle ORM
- **表前缀：** `app-template_`
- **时区支持：** 是（withTimezone: true）

## 注意事项

1. 所有主键都使用 UUID 格式
2. 时间戳字段都支持时区
3. 用户删除时会级联删除相关的创作记录
4. 资源URL必须唯一
5. 订阅计划名称必须唯一
