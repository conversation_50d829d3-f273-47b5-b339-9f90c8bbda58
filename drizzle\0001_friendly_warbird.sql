CREATE TABLE IF NOT EXISTS "app-template_asset" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user-id" varchar(255) NOT NULL,
	"url" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "app-template_asset_url_unique" UNIQUE("url")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "app-template_creation" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user-id" varchar(255),
	"prompt" text,
	"ai_model" varchar(100),
	"credits_consumed" integer,
	"created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "app-template_subscription_plan" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"plan_name" varchar(50) NOT NULL,
	"price_monthly" numeric(10, 2) NOT NULL,
	"credits_per_month" integer NOT NULL,
	"features" jsonb,
	CONSTRAINT "app-template_subscription_plan_plan_name_unique" UNIQUE("plan_name")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "app-template_asset" ADD CONSTRAINT "app-template_asset_user-id_app-template_user_id_fk" FOREIGN KEY ("user-id") REFERENCES "public"."app-template_user"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "app-template_creation" ADD CONSTRAINT "app-template_creation_user-id_app-template_user_id_fk" FOREIGN KEY ("user-id") REFERENCES "public"."app-template_user"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "app-template_creation" ADD CONSTRAINT "app-template_creation_user-id_app-template_asset_id_fk" FOREIGN KEY ("user-id") REFERENCES "public"."app-template_asset"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
