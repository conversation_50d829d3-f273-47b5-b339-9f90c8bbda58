import type { ReactNode } from "react";
import * as motion from "motion/react-client";

interface FeatureCardProps {
  icon: ReactNode;
  title: string;
  children: ReactNode;
}

export function FeatureCard({ icon, title, children }: FeatureCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.05 }}
      className="rounded-xl bg-white/5 p-6 backdrop-blur-sm"
    >
      <div className="mb-4 h-12 w-12 text-purple-500">{icon}</div>
      <h3 className="mb-4 text-xl font-bold text-white">{title}</h3>
      <ul className="space-y-3 text-zinc-300">{children}</ul>
    </motion.div>
  );
}

interface FeatureItemProps {
  icon: ReactNode;
  children: ReactNode;
}

export function FeatureItem({ icon, children }: FeatureItemProps) {
  return (
    <li className="flex items-center gap-2">
      <div className="h-4 w-4 text-violet-400">{icon}</div>
      {children}
    </li>
  );
}
