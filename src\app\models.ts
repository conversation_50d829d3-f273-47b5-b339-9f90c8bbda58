export const modelList = [
  {
    name: "Flux1.1 Pro Ultra",
    type: "text-to-image",
    uri: "/black-forest-labs/flux-1.1-pro-ultra/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          default: "ein fantastisches bild",
          description: "Text prompt for image generation",
        },
      },
      {
        name: "aspect_ratio",
        schema: {
          type: ["string"],
          default: "1:1",
          enum: [
            "1:1",
            "16:9",
            "21:9",
            "3:2",
            "2:3",
            "4:5",
            "5:4",
            "3:4",
            "4:3",
            "9:16",
            "9:21",
          ],
          description: "Aspect ratio of the image",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "raw",
        schema: {
          type: ["boolean"],
          default: false,
          description: "Generate less processed, more natural-looking images",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
  {
    name: "Flux1.1 Pro",
    type: "text-to-image",
    uri: "/black-forest-labs/flux-1.1-pro/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          default: "ein fantastisches bild",
          description: "Text prompt for image generation",
        },
      },
      {
        name: "aspect_ratio",
        schema: {
          type: ["string"],
          default: "1:1",
          enum: [
            "1:1",
            "16:9",
            "21:9",
            "3:2",
            "2:3",
            "4:5",
            "5:4",
            "3:4",
            "4:3",
            "9:16",
            "9:21",
          ],
          description: "Aspect ratio of the image",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
  {
    name: "Flux Dev",
    type: "text-to-image",
    uri: "/black-forest-labs/flux-dev/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          default: "ein fantastisches bild",
          description: "Text prompt for image generation",
        },
      },
      {
        name: "aspect_ratio",
        schema: {
          type: ["string"],
          default: "1:1",
          enum: [
            "1:1",
            "16:9",
            "21:9",
            "3:2",
            "2:3",
            "4:5",
            "5:4",
            "3:4",
            "4:3",
            "9:16",
            "9:21",
          ],
          description: "Aspect ratio of the image",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image.",
        },
      },
    ],
  },
  {
    name: "Flux Schnell",
    type: "text-to-image",
    uri: "/black-forest-labs/flux-schnell/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          default: "ein fantastisches bild",
          description: "Text prompt for image generation",
        },
      },
      {
        name: "aspect_ratio",
        schema: {
          type: ["string"],
          default: "1:1",
          enum: [
            "1:1",
            "16:9",
            "21:9",
            "3:2",
            "2:3",
            "4:5",
            "5:4",
            "3:4",
            "4:3",
            "9:16",
            "9:21",
          ],
          description: "Aspect ratio of the image",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image.",
        },
      },
    ],
  },
  {
    name: "Flux Fill Pro",
    type: "image-to-image",
    uri: "/black-forest-labs/flux-fill-pro/predictions",
    properties: [
      {
        name: "image",
        schema: {
          type: ["string"],
          description: "Base64 encoded image",
        },
      },
      {
        name: "prompt",
        schema: {
          type: ["string"],
          description: "Text prompt for image generation",
        },
      },
      {
        name: "mask",
        schema: {
          type: ["string"],
          description:
            "A black-and-white image that describes the part of the image to inpaint. Black areas will be preserved while white areas will be inpainted. Must have the same size as image. Optional if you provide an alpha mask in the original image. Must be jpeg, png, gif, or webp.",
        },
      },
      {
        name: "outpaint",
        schema: {
          type: ["string", "null"],
          default: "None",
          enum: [
            "None",
            "Zoom out 1.5x",
            "Zoom out 2x",
            "Make square",
            "Left outpaint",
            "Right outpaint",
            "Top outpaint",
            "Bottom outpaint",
          ],
          description: "A quick option for outpainting an input image.",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
  {
    name: "Flux Fill Pro",
    type: "image-to-image",
    uri: "/black-forest-labs/flux-fill-pro/predictions",
    properties: [
      {
        name: "image",
        schema: {
          type: ["string"],
          description: "Base64 encoded image",
        },
      },
      {
        name: "prompt",
        schema: {
          type: ["string"],
          description: "Text prompt for image generation",
        },
      },
      {
        name: "mask",
        schema: {
          type: ["string"],
          description:
            "A black-and-white image that describes the part of the image to inpaint. Black areas will be preserved while white areas will be inpainted. Must have the same size as image. Optional if you provide an alpha mask in the original image. Must be jpeg, png, gif, or webp.",
        },
      },
      {
        name: "outpaint",
        schema: {
          type: ["string", "null"],
          default: "None",
          enum: [
            "None",
            "Zoom out 1.5x",
            "Zoom out 2x",
            "Make square",
            "Left outpaint",
            "Right outpaint",
            "Top outpaint",
            "Bottom outpaint",
          ],
          description: "A quick option for outpainting an input image.",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
  {
    name: "Flux Canny Pro",
    type: "image-to-image",
    uri: "/black-forest-labs/flux-canny-pro/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          description: "Text prompt for image generation",
        },
      },
      {
        name: "control_image",
        schema: {
          type: ["string"],
          description: "Image to use as control input.",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
  {
    name: "Flux Depth Pro",
    type: "image-to-image",
    uri: "/black-forest-labs/flux-depth-pro/predictions",
    properties: [
      {
        name: "prompt",
        schema: {
          type: ["string"],
          description: "Text prompt for image generation",
        },
      },
      {
        name: "control_image",
        schema: {
          type: ["string"],
          description: "Image to use as control input.",
        },
      },
      {
        name: "seed",
        schema: {
          type: ["number", "null"],
          description: "Optional seed for reproducibility.",
        },
      },
      {
        name: "output_format",
        schema: {
          type: ["string", "null"],
          default: "jpeg",
          enum: ["jpeg", "png"],
          description: "Output format for the generated image. ",
        },
      },
    ],
  },
];
