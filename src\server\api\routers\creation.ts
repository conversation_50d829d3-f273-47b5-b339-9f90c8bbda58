import { z } from "zod";

import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "@/server/api/trpc";
import { fal } from "@fal-ai/client";

export const creationRouter = createTRPCRouter({
  create: protectedProcedure
    .input(z.object({ prompt: z.string(), imageUrl: z.string() }))
    .mutation(async({ input }) => {
      const { prompt, imageUrl } = input;

      
      
      // const result = await fal.subscribe("fal-ai/flux/dev", {
      //   input: {
      //     prompt,
      //     image_size: "square_hd",
      //   },
      //   pollInterval: 5000,
      //   logs: true,
      //   onQueueUpdate(update) {
      //     console.log("queue update", update);
      //   },
      // }).catch(error => {
      //   console.error("Error:", JSON.stringify(error));
      //   throw error;
      // });

      // return {result};
    }),
});