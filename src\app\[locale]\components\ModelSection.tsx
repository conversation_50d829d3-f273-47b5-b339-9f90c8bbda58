import { <PERSON><PERSON><PERSON>, Zap, Image, PenTool, Lay<PERSON>, Cpu } from "lucide-react";
import * as motion from "motion/react-client";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';

interface ModelCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  isPro?: boolean;
  isUltra?: boolean;
  proLabel: string;
  ultraLabel: string;
}

function ModelCard({
  title,
  description,
  icon,
  isPro = false,
  isUltra = false,
  proLabel,
  ultraLabel,
}: ModelCardProps) {
  return (
    <motion.div
      whileHover={{ scale: 1.03 }}
      className={`rounded-xl p-6 backdrop-blur-sm ${
        isUltra
          ? "border border-pink-500/20 bg-gradient-to-br from-pink-500/10 to-purple-600/10"
          : isPro
            ? "border border-violet-500/20 bg-gradient-to-br from-violet-500/10 to-indigo-600/10"
            : "bg-white/5"
      }`}
    >
      <div className="mb-4 flex items-center gap-3">
        <div
          className={`rounded-lg p-2 ${
            isUltra
              ? "bg-pink-500/20 text-pink-400"
              : isPro
                ? "bg-violet-500/20 text-violet-400"
                : "bg-indigo-500/20 text-indigo-400"
          }`}
        >
          {icon}
        </div>
        <div>
          <h3 className="flex items-center gap-2 text-xl font-bold text-white">
            {title}
            {isPro && (
              <span className="rounded-full bg-violet-500/20 px-2 py-0.5 text-xs font-medium text-violet-300">
                {proLabel}
              </span>
            )}
            {isUltra && (
              <span className="rounded-full bg-pink-500/20 px-2 py-0.5 text-xs font-medium text-pink-300">
                {ultraLabel}
              </span>
            )}
          </h3>
        </div>
      </div>
      <p className="text-sm text-zinc-300">{description}</p>
    </motion.div>
  );
}

export function ModelSection() {
  const t = useTranslations('models');
  
  return (
    <section className="py-16">
      <ScrollAnimation direction="up" delay={0.2} className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        <ModelCard
          title={t('fluxSchnell.title')}
          description={t('fluxSchnell.description')}
          icon={<Zap className="h-6 w-6" />}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />

        <ModelCard
          title={t('fluxDev.title')}
          description={t('fluxDev.description')}
          icon={<Cpu className="h-6 w-6" />}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />

        <ModelCard
          title={t('fluxPro.title')}
          description={t('fluxPro.description')}
          icon={<Sparkles className="h-6 w-6" />}
          isPro={true}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />

        <ModelCard
          title={t('fluxUltra.title')}
          description={t('fluxUltra.description')}
          icon={<Layers className="h-6 w-6" />}
          isUltra={true}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />

        <ModelCard
          title={t('fluxFill.title')}
          description={t('fluxFill.description')}
          icon={<Image className="h-6 w-6" />}
          isPro={true}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />

        <ModelCard
          title={t('fluxDepthCanny.title')}
          description={t('fluxDepthCanny.description')}
          icon={<PenTool className="h-6 w-6" />}
          isPro={true}
          proLabel={t('proLabel')}
          ultraLabel={t('ultraLabel')}
        />
      </div>
    </section>
  );
}
