"use client";

import { useLocale, useTranslations } from "next-intl";
import { routing, usePathname, useRouter } from "@/i18n/routing";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useParams } from "next/navigation";
import { Globe } from "lucide-react";

export function LocaleSwitcher() {
  const locale = useLocale();
  const t = useTranslations('locale');

  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();

  const handleChange = (value: string) => {
    router.replace(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      { pathname, params },
      { locale: value },
    );
  };

  return (
    <Select defaultValue={locale} onValueChange={handleChange}>
      <SelectTrigger
        className="h-8 w-10 px-1 border-0 bg-transparent py-0 text-white/60 transition-colors duration-200 hover:bg-white/5 hover:text-white/90 focus:ring-0 focus:ring-offset-0"
        aria-label={t('switchLanguage')}
      >
        <Globe className="h-4 w-4 opacity-70" />
        <span className="sr-only">{t('switchLanguage')}</span>
      </SelectTrigger>
      <SelectContent
        className="z-[200] min-w-[100px] rounded-lg border-white/10 bg-indigo-950/90 text-white/90 shadow-lg backdrop-blur-sm"
        align="end"
      >
        {routing.locales.map((cur) => (
          <SelectItem
            key={cur}
            value={cur}
            className="py-1.5 text-sm text-white/80 hover:bg-white/10 hover:text-white focus:bg-white/10 focus:text-white data-[state=checked]:bg-white/15 data-[state=checked]:text-white"
          >
            {t(`languages.${cur}`)}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
