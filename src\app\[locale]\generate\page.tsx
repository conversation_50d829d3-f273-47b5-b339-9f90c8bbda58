"use client";

import { useState, useRef, useCallback } from "react";
import { useTranslations } from "next-intl";
import { motion } from "motion/react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { BackgroundBlurs } from "../components/BackgroundBlurs";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Loader2,
  Download,
  Trash2,
  Wand2,
  Upload,
  RotateCcw,
} from "lucide-react";
import { api } from "@/trpc/react";
import { env } from "@/env";

// Types

interface ProcessingSettings {
  quality: number;
  preserveAspect: boolean;
  outputFormat: "jpg" | "png" | "webp";
  enhanceDetails: boolean;
}





export default function Generate() {
  const t = useTranslations("Generate");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Enhanced state management
  const [prompt, setPrompt] = useState<string>("");
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processingProgress, setProcessingProgress] = useState<number>(0);
  const [result, setResult] = useState<string | null>(null);

  const [isDragging, setIsDragging] = useState<boolean>(false);


  const [error, setError] = useState<string | null>(null);



  const uploadMutation = api.upload.upload.useMutation();

  // Enhanced image processing with progress tracking
  const processImage = useCallback(async () => {
    if (!uploadedImage || !prompt.trim()) {
      setError("Please upload an image and enter a prompt");
      return;
    }

    setIsProcessing(true);
    setResult(null);
    setError(null);
    setProcessingProgress(0);

    try {
      // Simulate processing with progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 15;
        });
      }, 200);



      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setProcessingProgress(100);

      // Simulate processing result
      const processedImage = uploadedImage; // In real app, this would be the processed image
      setResult(processedImage);



      // Reset progress after a short delay
      setTimeout(() => setProcessingProgress(0), 1000);
    } catch (error) {
      setError("Failed to process image. Please try again.");
      console.error("Processing error:", error);
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedImage, prompt, result]);

  // Enhanced image upload with validation and multiple format support
  const handleImageUpload = useCallback((file: File) => {
    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/webp",
      "image/gif",
    ];


    uploadMutation.mutateAsync({
      fileType: file.type,
      fileSize: file.size,
    }).then((result) => {
      console.log("Upload result:", result);
      fetch(result.signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      }).then(()=>{
        setUploadedImage(`${env.NEXT_PUBLIC_R2_PUBLIC_URL}${result.key}`);
        setResult(null);
      }).catch(()=>{
        //
      })
    }).catch(()=>{
      //
    });

    if (!allowedTypes.includes(file.type)) {
      setError("Please upload a valid image file (JPG, PNG, WEBP, or GIF)");
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      setError("File size must be less than 10MB");
      return;
    }

    setError(null);
  }, []);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Enhanced drag and drop with multiple file support
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      const imageFiles = files.filter((file) => file.type.startsWith("image/"));

      if (imageFiles.length === 0) {
        setError("Please drop valid image files");
        return;
      }

      // Handle single file
      if (imageFiles[0]) {
        handleImageUpload(imageFiles[0]);
      }
    },
    [handleImageUpload],
  );

  // Utility functions
  const resetToOriginal = useCallback(() => {
    if (uploadedImage) {
      setResult(uploadedImage);
    }
  }, [uploadedImage]);



  const downloadImage = useCallback(
    (imageUrl: string, filename = "edited-image") => {
      const link = document.createElement("a");
      link.href = imageUrl;
      link.download = `${filename}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    [],
  );





  return (
    <div className="min-h-screen w-full bg-gradient-to-b from-background to-background/80">
      <BackgroundBlurs />

      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8 text-center">
          <h2 className="mb-2 text-2xl font-semibold text-foreground/90">
            {t("title")}
          </h2>
          <p className="text-muted-foreground">
            {t("subtitle")}
          </p>
        </div>

        {/* Primary Upload Interface - Always Visible */}
        <div className="mx-auto max-w-4xl space-y-8">
          {/* Upload Area */}
          {!uploadedImage ? (
            <Card className="border-none bg-card/50 backdrop-blur-sm">
              <CardContent className="p-8">
                <motion.div
                  className={`w-full cursor-pointer rounded-xl border-2 border-dashed p-8 text-center transition-all md:p-12 ${
                    isDragging
                      ? "scale-[1.02] border-primary bg-primary/10 shadow-lg"
                      : "border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/5"
                  }`}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => fileInputRef.current?.click()}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="space-y-4">
                    <Upload className="mx-auto h-12 w-12 text-primary/60 md:h-16 md:w-16" />
                    <div>
                      <h3 className="mb-2 text-lg font-medium md:text-xl">
                        {t("image.upload")}
                      </h3>
                      <p className="mb-4 text-sm text-muted-foreground">
                        Supports JPG, PNG, WEBP, GIF (max 10MB)
                      </p>
                    </div>
                    <Button variant="outline" size="lg">
                      <Upload className="mr-2 h-4 w-4" />
                      {t("image.selectButton")}
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleFileInputChange}
                  />
                </motion.div>
              </CardContent>
            </Card>
          ) : (
            <Card className="border-none bg-card/50 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium">
                    {t("image.original")}
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setUploadedImage(null);
                      setResult(null);
                      setError(null);
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Original Image */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="overflow-hidden rounded-xl bg-muted/20"
                >
                  <img
                    src={uploadedImage}
                    alt="Original image"
                    className="w-full object-contain max-h-[50vh]"
                  />
                </motion.div>

                {/* Editing Interface */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="prompt" className="text-sm font-medium">
                      编辑指令
                    </Label>
                    <Textarea
                      id="prompt"
                      placeholder={t("prompt.placeholder")}
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="min-h-[100px] resize-none"
                      disabled={isProcessing}
                    />
                  </div>

                  {/* Processing progress */}
                  {isProcessing && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="space-y-2"
                    >
                      <div className="flex items-center justify-between text-sm">
                        <span>Processing your image...</span>
                        <span>{Math.round(processingProgress)}%</span>
                      </div>
                      <div className="h-2 w-full rounded-full bg-muted">
                        <motion.div
                          className="h-2 rounded-full bg-primary"
                          initial={{ width: 0 }}
                          animate={{ width: `${processingProgress}%` }}
                          transition={{ duration: 0.3 }}
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Generate Button */}
                  <Button
                    size="lg"
                    disabled={!prompt.trim() || isProcessing}
                    onClick={processImage}
                    className="w-full"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("processing")}
                      </>
                    ) : (
                      <>
                        <Wand2 className="mr-2 h-4 w-4" />
                        {t("generate")}
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Result Section */}
          {result && (
            <Card className="border-none bg-card/50 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-medium">
                    编辑结果
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadImage(result, "edited-image")}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetToOriginal}
                    >
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Reset
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="overflow-hidden rounded-xl bg-muted/20"
                >
                  <img
                    src={result}
                    alt="Edited image"
                    className="w-full object-contain max-h-[50vh]"
                  />
                </motion.div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
