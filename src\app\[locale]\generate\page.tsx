"use client";

import { useState, useRef, use<PERSON><PERSON>back, useEffect } from "react";
import { useTranslations } from "next-intl";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BackgroundBlurs } from "../components/BackgroundBlurs";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Loader2,
  Download,
  Trash2,
  Sparkles,
  Wand2,
  Image,
  Upload,
  Settings,
  Undo2,
  RefreshCw,
  Redo2,
  Eye,
  EyeOff,
  Lightbulb,
  Zap,
  Palette,
  Scissors,
  RotateCcw,
  Copy,
  AlertCircle,
} from "lucide-react";
import { api } from "@/trpc/react";
import { env } from "@/env";

// Types

interface ProcessingSettings {
  quality: number;
  preserveAspect: boolean;
  outputFormat: "jpg" | "png" | "webp";
  enhanceDetails: boolean;
}

interface PromptSuggestion {
  id: string;
  text: string;
  category: "enhance" | "retouch" | "background" | "style" | "creative";
  icon: React.ReactNode;
}

// Enhanced prompt suggestions
const promptSuggestions: PromptSuggestion[] = [
  {
    id: "remove-bg",
    text: "Remove background completely",
    category: "background",
    icon: <Scissors className="h-4 w-4" />,
  },
  {
    id: "sunset-bg",
    text: "Change background to beautiful sunset",
    category: "background",
    icon: <Palette className="h-4 w-4" />,
  },
  {
    id: "enhance-quality",
    text: "Enhance image quality and sharpness",
    category: "enhance",
    icon: <Sparkles className="h-4 w-4" />,
  },
  {
    id: "portrait-retouch",
    text: "Professional portrait retouching",
    category: "retouch",
    icon: <Wand2 className="h-4 w-4" />,
  },
  {
    id: "artistic-style",
    text: "Apply artistic painting style",
    category: "style",
    icon: <Palette className="h-4 w-4" />,
  },
  {
    id: "vintage-filter",
    text: "Add vintage film effect",
    category: "style",
    icon: <Image className="h-4 w-4" />,
  },
  {
    id: "make-smile",
    text: "Make the person smile naturally",
    category: "retouch",
    icon: <Wand2 className="h-4 w-4" />,
  },
  {
    id: "dramatic-lighting",
    text: "Add dramatic lighting effects",
    category: "enhance",
    icon: <Zap className="h-4 w-4" />,
  },
];



export default function Generate() {
  const t = useTranslations("Generate");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Enhanced state management
  const [prompt, setPrompt] = useState<string>("");
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [processingProgress, setProcessingProgress] = useState<number>(0);
  const [result, setResult] = useState<string | null>(null);
  const [activeFeature, setActiveFeature] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  const [showComparison, setShowComparison] = useState<boolean>(false);
  const [showSuggestions, setShowSuggestions] = useState<boolean>(true);
  const [selectedSuggestion, setSelectedSuggestion] = useState<string | null>(
    null,
  );
  const [error, setError] = useState<string | null>(null);

  // Processing settings
  const [settings, setSettings] = useState<ProcessingSettings>({
    quality: 90,
    preserveAspect: true,
    outputFormat: "jpg",
    enhanceDetails: true,
  });

  // Undo/Redo state
  const [undoStack, setUndoStack] = useState<string[]>([]);
  const [redoStack, setRedoStack] = useState<string[]>([]);



  const uploadMutation = api.upload.upload.useMutation();

  // Enhanced image processing with progress tracking
  const processImage = useCallback(async () => {
    if (!uploadedImage || !prompt.trim()) {
      setError("Please upload an image and enter a prompt");
      return;
    }

    setIsProcessing(true);
    setResult(null);
    setError(null);
    setProcessingProgress(0);

    try {
      // Simulate processing with progress updates
      const progressInterval = setInterval(() => {
        setProcessingProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 15;
        });
      }, 200);

      // Add to undo stack before processing
      if (result) {
        setUndoStack((prev) => [...prev, result]);
        setRedoStack([]); // Clear redo stack when new action is performed
      }

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      clearInterval(progressInterval);
      setProcessingProgress(100);

      // Simulate processing result
      const processedImage = uploadedImage; // In real app, this would be the processed image
      setResult(processedImage);



      // Reset progress after a short delay
      setTimeout(() => setProcessingProgress(0), 1000);
    } catch (error) {
      setError("Failed to process image. Please try again.");
      console.error("Processing error:", error);
    } finally {
      setIsProcessing(false);
    }
  }, [uploadedImage, prompt, result]);

  // Enhanced image upload with validation and multiple format support
  const handleImageUpload = useCallback((file: File) => {
    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/webp",
      "image/gif",
    ];


    uploadMutation.mutateAsync({
      fileType: file.type,
      fileSize: file.size,
    }).then((result) => {
      console.log("Upload result:", result);
      fetch(result.signedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      }).then(()=>{
        setUploadedImage(`${env.NEXT_PUBLIC_R2_PUBLIC_URL}${result.key}`);
        setResult(null);
        setShowSuggestions(true);
      }).catch(()=>{
        //
      })
    }).catch(()=>{
      //
    });

    if (!allowedTypes.includes(file.type)) {
      setError("Please upload a valid image file (JPG, PNG, WEBP, or GIF)");
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      setError("File size must be less than 10MB");
      return;
    }

    setError(null);
  }, []);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
  };

  // Enhanced drag and drop with multiple file support
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragging to false if we're leaving the drop zone entirely
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);
      const imageFiles = files.filter((file) => file.type.startsWith("image/"));

      if (imageFiles.length === 0) {
        setError("Please drop valid image files");
        return;
      }

      // Handle single file
      if (imageFiles[0]) {
        handleImageUpload(imageFiles[0]);
      }
    },
    [handleImageUpload],
  );

  // Utility functions
  const resetToOriginal = useCallback(() => {
    if (uploadedImage) {
      setResult(uploadedImage);
      setUndoStack((prev) => [...prev, result || ""]);
      setRedoStack([]);
    }
  }, [uploadedImage, result]);

  const handleUndo = useCallback(() => {
    if (undoStack.length > 0 && result) {
      const previousState = undoStack[undoStack.length - 1];
      if (previousState) {
        setRedoStack((prev) => [...prev, result]);
        setUndoStack((prev) => prev.slice(0, -1));
        setResult(previousState);
      }
    }
  }, [undoStack, result]);

  const handleRedo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[redoStack.length - 1];
      if (nextState) {
        setUndoStack((prev) => [...prev, result ?? ""]);
        setRedoStack((prev) => prev.slice(0, -1));
        setResult(nextState);
      }
    }
  }, [redoStack, result]);

  const downloadImage = useCallback(
    (imageUrl: string, filename: string = "edited-image") => {
      const link = document.createElement("a");
      link.href = imageUrl;
      link.download = `${filename}.${settings.outputFormat}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    [settings.outputFormat],
  );

  // Enhanced feature handling with suggestions
  const handleFeatureClick = useCallback(
    (feature: string) => {
      setActiveFeature(feature);
      setPrompt(t(`features.${feature}.template`));
      setSelectedSuggestion(null);
    },
    [t],
  );

  const handleSuggestionClick = useCallback((suggestion: PromptSuggestion) => {
    setPrompt(suggestion.text);
    setSelectedSuggestion(suggestion.id);
    setActiveFeature(suggestion.category);
  }, []);

  // 功能列表
  const features = [
    { id: "enhance", icon: <Sparkles className="h-4 w-4" /> },
    { id: "retouch", icon: <Wand2 className="h-4 w-4" /> },
    { id: "background", icon: <Image className="h-4 w-4" /> },
    { id: "style", icon: <Settings className="h-4 w-4" /> },
    { id: "resize", icon: <RefreshCw className="h-4 w-4" /> },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-b from-background to-background/80">
      <BackgroundBlurs />

      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <div className="mb-8 text-center">
          <h2 className="mb-2 text-2xl font-semibold text-foreground/90">
            {t("title")}
          </h2>
          <p className="text-muted-foreground">
            {t("subtitle")}
          </p>
        </div>

        {/* Primary Upload Interface - Always Visible */}
        <div className="mx-auto max-w-6xl space-y-8">
          {/* Enhanced editing area with tabs */}
          <Card className="border-none bg-card/50 backdrop-blur-sm">
            <CardContent className="space-y-6 pt-6">
              {/* Enhanced image upload area */}
              {!uploadedImage && !result ? (
                <motion.div
                  className={`w-full cursor-pointer rounded-xl border-2 border-dashed p-8 text-center transition-all md:p-12 ${
                    isDragging
                      ? "scale-[1.02] border-primary bg-primary/10 shadow-lg"
                      : "border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/5"
                  }`}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => fileInputRef.current?.click()}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <div className="space-y-4">
                    <Upload className="mx-auto h-12 w-12 text-primary/60 md:h-16 md:w-16" />
                    <div>
                      <h3 className="mb-2 text-lg font-medium md:text-xl">
                        {t("image.upload")}
                      </h3>
                      <p className="mb-4 text-sm text-muted-foreground">
                        Supports JPG, PNG, WEBP, GIF (max 10MB)
                      </p>
                    </div>
                    <Button variant="outline" size="lg">
                      <Upload className="mr-2 h-4 w-4" />
                      {t("image.selectButton")}
                    </Button>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    onChange={handleFileInputChange}
                  />
                </motion.div>
              ) : (
                <div className="w-full space-y-6">
                  {/* Enhanced image display with comparison view */}
                  <Tabs
                    value={showComparison && result ? "comparison" : "single"}
                    className="w-full"
                  >
                    <div className="mb-4 flex items-center justify-between">
                      <TabsList className="grid w-fit grid-cols-2">
                        <TabsTrigger
                          value="single"
                          onClick={() => setShowComparison(false)}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Single View
                        </TabsTrigger>
                        <TabsTrigger
                          value="comparison"
                          onClick={() => setShowComparison(true)}
                          disabled={!result}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          Compare
                        </TabsTrigger>
                      </TabsList>

                      <div className="flex items-center gap-2">
                        {result && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleUndo}
                              disabled={undoStack.length === 0}
                            >
                              <Undo2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleRedo}
                              disabled={redoStack.length === 0}
                            >
                              <Redo2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                downloadImage(result, "edited-image")
                              }
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </Button>
                          </>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setUploadedImage(null);
                            setResult(null);
                            setError(null);
                          }}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Clear
                        </Button>
                      </div>
                    </div>

                    <TabsContent value="single" className="space-y-4">
                      {/* Single image view */}
                      <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="relative overflow-hidden rounded-xl bg-muted/20"
                      >
                        <img
                          src={result || uploadedImage || ""}
                          alt={result ? "Edited image" : "Original image"}
                          className="max-h-[50vh] w-full object-contain"
                        />
                        <div className="absolute left-2 top-2 rounded bg-black/50 px-2 py-1 text-xs text-white">
                          {result ? "Edited" : "Original"}
                        </div>
                      </motion.div>
                    </TabsContent>

                    <TabsContent value="comparison" className="space-y-4">
                      {/* Before/After comparison */}
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="relative overflow-hidden rounded-xl bg-muted/20"
                        >
                          <img
                            src={uploadedImage || ""}
                            alt="Original image"
                            className="max-h-[40vh] w-full object-contain"
                          />
                          <div className="absolute left-2 top-2 rounded bg-black/50 px-2 py-1 text-xs text-white">
                            Before
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="relative overflow-hidden rounded-xl bg-muted/20"
                        >
                          <img
                            src={result || uploadedImage || ""}
                            alt="Edited image"
                            className="max-h-[40vh] w-full object-contain"
                          />
                          <div className="absolute left-2 top-2 rounded bg-black/50 px-2 py-1 text-xs text-white">
                            After
                          </div>
                        </motion.div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              )}

              {/* Enhanced prompt and features area */}
              {uploadedImage && (
                <div className="space-y-6">
                  {/* Prompt suggestions */}
                  {showSuggestions && !result && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Lightbulb className="h-4 w-4 text-primary" />
                          <span className="text-sm font-medium">
                            Quick Suggestions
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSuggestions(false)}
                        >
                          <EyeOff className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-4">
                        {promptSuggestions.slice(0, 8).map((suggestion) => (
                          <motion.div
                            key={suggestion.id}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              variant={
                                selectedSuggestion === suggestion.id
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              className="h-auto w-full justify-start p-3 text-left"
                              onClick={() => handleSuggestionClick(suggestion)}
                            >
                              <div className="flex items-start gap-2">
                                {suggestion.icon}
                                <span className="text-xs leading-tight">
                                  {suggestion.text}
                                </span>
                              </div>
                            </Button>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Enhanced prompt input */}
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="prompt" className="text-sm font-medium">
                        Editing Instructions
                      </Label>
                      <div className="relative">
                        <Textarea
                          id="prompt"
                          placeholder={t("prompt.placeholder")}
                          value={prompt}
                          onChange={(e) => setPrompt(e.target.value)}
                          className="min-h-[80px] resize-none pr-12"
                          disabled={isProcessing}
                        />
                        {!showSuggestions && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute right-2 top-2"
                            onClick={() => setShowSuggestions(true)}
                          >
                            <Lightbulb className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Processing progress */}
                    {isProcessing && (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="space-y-2"
                      >
                        <div className="flex items-center justify-between text-sm">
                          <span>Processing your image...</span>
                          <span>{Math.round(processingProgress)}%</span>
                        </div>
                        <div className="h-2 w-full rounded-full bg-muted">
                          <motion.div
                            className="h-2 rounded-full bg-primary"
                            initial={{ width: 0 }}
                            animate={{ width: `${processingProgress}%` }}
                            transition={{ duration: 0.3 }}
                          />
                        </div>
                      </motion.div>
                    )}

                    {/* Action buttons */}
                    <div className="flex flex-col gap-3 sm:flex-row">
                      <Button
                        size="lg"
                        disabled={!prompt.trim() || isProcessing}
                        onClick={processImage}
                        className="flex-1"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {t("processing")}
                          </>
                        ) : (
                          <>
                            <Wand2 className="mr-2 h-4 w-4" />
                            {t("generate")}
                          </>
                        )}
                      </Button>

                      {result && (
                        <Button
                          variant="outline"
                          size="lg"
                          onClick={resetToOriginal}
                        >
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Enhanced feature categories */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Quick Actions</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {features.map((feature) => (
                        <motion.div
                          key={feature.id}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant={
                              activeFeature === feature.id
                                ? "default"
                                : "outline"
                            }
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={() => handleFeatureClick(feature.id)}
                          >
                            {feature.icon}
                            <span>{t(`features.${feature.id}.name`)}</span>
                          </Button>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* Advanced Settings Panel */}
                  <Card className="border-none bg-muted/20">
                    <CardContent className="space-y-4 p-4">
                      <div className="mb-3 flex items-center gap-2">
                        <Settings className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          Advanced Settings
                        </span>
                      </div>

                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        {/* Quality Setting */}
                        <div className="space-y-2">
                          <Label htmlFor="quality" className="text-xs">
                            Output Quality: {settings.quality}%
                          </Label>
                          <Slider
                            id="quality"
                            min={50}
                            max={100}
                            step={5}
                            value={[settings.quality]}
                            onValueChange={(value) =>
                              setSettings((prev) => ({
                                ...prev,
                                quality: value[0] ?? 90,
                              }))
                            }
                            className="w-full"
                          />
                        </div>

                        {/* Output Format */}
                        <div className="space-y-2">
                          <Label className="text-xs">Output Format</Label>
                          <div className="flex gap-2">
                            {(["jpg", "png", "webp"] as const).map((format) => (
                              <Button
                                key={format}
                                variant={
                                  settings.outputFormat === format
                                    ? "default"
                                    : "outline"
                                }
                                size="sm"
                                onClick={() =>
                                  setSettings((prev) => ({
                                    ...prev,
                                    outputFormat: format,
                                  }))
                                }
                                className="text-xs"
                              >
                                {format.toUpperCase()}
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Preserve Aspect Ratio */}
                        <div className="flex items-center justify-between">
                          <Label htmlFor="preserve-aspect" className="text-xs">
                            Preserve Aspect Ratio
                          </Label>
                          <Switch
                            id="preserve-aspect"
                            checked={settings.preserveAspect}
                            onCheckedChange={(checked) =>
                              setSettings((prev) => ({
                                ...prev,
                                preserveAspect: checked,
                              }))
                            }
                          />
                        </div>

                        {/* Enhance Details */}
                        <div className="flex items-center justify-between">
                          <Label htmlFor="enhance-details" className="text-xs">
                            Enhance Details
                          </Label>
                          <Switch
                            id="enhance-details"
                            checked={settings.enhanceDetails}
                            onCheckedChange={(checked) =>
                              setSettings((prev) => ({
                                ...prev,
                                enhanceDetails: checked,
                              }))
                            }
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </div>


      </div>
    </div>
  );
}
