"use client";

import { useState, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import {
  Clock,
  Download,
  Copy,
  Trash2,
  ChevronLeft,
  ChevronRight
} from "lucide-react";

interface HistoryItem {
  id: number;
  originalImage: string;
  resultImage: string;
  prompt: string;
  timestamp: string;
  settings?: {
    quality: number;
    preserveAspect: boolean;
    outputFormat: 'jpg' | 'png' | 'webp';
    enhanceDetails: boolean;
  };
}

interface FullWidthHistoryGalleryProps {
  history: HistoryItem[];
  setHistory: (history: HistoryItem[]) => void;
  downloadImage: (imageUrl: string, filename: string) => void;
  setUploadedImage: (image: string | null) => void;
  setPrompt: (prompt: string) => void;
  setResult: (result: string | null) => void;
}



// Individual Gallery Item Component
interface GalleryItemProps {
  item: HistoryItem;
  index: number;
  onDownload: (imageUrl: string, filename: string) => void;
  onReuse: (item: HistoryItem) => void;
  onDelete: (id: number) => void;
}

function GalleryItem({ item, index, onDownload, onReuse, onDelete }: GalleryItemProps) {
  const [showComparison, setShowComparison] = useState(false);
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle inline comparison slider
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = e.touches[0].clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ delay: index * 0.05 }}
      className="group relative rounded-xl overflow-hidden bg-background/50 backdrop-blur-sm border hover:shadow-2xl transition-all duration-300 hover:scale-[1.02]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => {
        setIsHovered(false);
        setShowComparison(false);
        setIsDragging(false);
      }}
    >
      {/* Image Container with Inline Comparison */}
      <div
        ref={containerRef}
        className="relative aspect-[4/3] overflow-hidden cursor-pointer"
        onClick={() => setShowComparison(!showComparison)}
        onMouseMove={showComparison ? handleMouseMove : undefined}
        onMouseDown={() => showComparison && setIsDragging(true)}
        onMouseUp={() => setIsDragging(false)}
        onTouchMove={showComparison ? handleTouchMove : undefined}
        onTouchStart={() => showComparison && setIsDragging(true)}
        onTouchEnd={() => setIsDragging(false)}
      >
        {showComparison ? (
          // Inline Before/After Comparison
          <>
            {/* After Image (Background) */}
            <img
              src={item.resultImage}
              alt="After"
              className="absolute inset-0 w-full h-full object-cover"
              draggable={false}
            />

            {/* Before Image (Clipped) */}
            <div
              className="absolute inset-0 overflow-hidden"
              style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
            >
              <img
                src={item.originalImage}
                alt="Before"
                className="w-full h-full object-cover"
                draggable={false}
              />
            </div>

            {/* Slider Line */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-white shadow-lg z-10 pointer-events-none"
              style={{ left: `${sliderPosition}%` }}
            >
              {/* Slider Handle */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white rounded-full shadow-lg flex items-center justify-center">
                <div className="flex gap-0.5">
                  <ChevronLeft className="h-2 w-2" />
                  <ChevronRight className="h-2 w-2" />
                </div>
              </div>
            </div>

            {/* Comparison Labels */}
            <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
              Before
            </div>
            <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
              After
            </div>
          </>
        ) : (
          // Normal Image Display
          <>
            <img
              src={item.resultImage}
              alt="Generated image"
              className="w-full h-full object-cover transition-all duration-300"
            />

            {/* Click to Compare Hint */}
            <div className={`absolute inset-0 bg-black/40 flex items-center justify-center transition-opacity duration-200 ${
              isHovered ? 'opacity-100' : 'opacity-0'
            }`}>
              <div className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white text-sm font-medium">
                Click to compare
              </div>
            </div>
          </>
        )}

        {/* Action Buttons Overlay */}
        <div className={`absolute inset-0 bg-black/60 transition-opacity duration-200 ${
          isHovered && !showComparison ? 'opacity-100' : 'opacity-0'
        }`}>
          <div className="absolute inset-0 flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                onDownload(item.resultImage, `history-${item.id}`);
              }}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                onReuse(item);
              }}
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-10 w-10 bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(item.id);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Quality Badge - Only show when not in comparison mode */}
        {!showComparison && item.settings && (
          <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium">
            {item.settings.quality}%
          </div>
        )}

        {/* Format Badge - Only show when not in comparison mode */}
        {!showComparison && item.settings && (
          <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium uppercase">
            {item.settings.outputFormat}
          </div>
        )}
      </div>

      {/* Minimal Content - Only timestamp */}
      <div className="p-3">
        <div className="flex items-center justify-center text-xs text-muted-foreground">
          <Clock className="h-3 w-3 mr-1" />
          <span>{item.timestamp}</span>
        </div>

        {/* Prompt as tooltip on hover */}
        {isHovered && (
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 bg-black/90 text-white text-xs px-3 py-2 rounded-lg max-w-xs text-center whitespace-normal z-20">
            {item.prompt}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/90"></div>
          </div>
        )}
      </div>
    </motion.div>
  );
}

export function FullWidthHistoryGallery({
  history,
  setHistory,
  downloadImage,
  setUploadedImage,
  setPrompt,
  setResult
}: FullWidthHistoryGalleryProps) {
  const handleReuse = useCallback((item: HistoryItem) => {
    setUploadedImage(item.originalImage);
    setPrompt(item.prompt);
    setResult(null);
  }, [setUploadedImage, setPrompt, setResult]);

  const handleDelete = useCallback((id: number) => {
    setHistory(history.filter(item => item.id !== id));
  }, [history, setHistory]);

  if (history.length === 0) {
    return null;
  }

  return (
    <>
      {/* Full-Width Gallery Section - No Card Wrapper */}
      <div className="w-full">
        {/* Simplified Gallery Header */}
        <div className="mb-8 px-4">
          <h3 className="text-2xl font-bold text-center">Your Creation Gallery</h3>
        </div>

        {/* Full-Width Grid - Fixed 3 Columns */}
        <div className="grid grid-cols-3 gap-6 px-4">
          <AnimatePresence>
            {history.map((item, index) => (
              <GalleryItem
                key={item.id}
                item={item}
                index={index}
                onDownload={downloadImage}
                onReuse={handleReuse}
                onDelete={handleDelete}
              />
            ))}
          </AnimatePresence>
        </div>
      </div>
    </>
  );
}
