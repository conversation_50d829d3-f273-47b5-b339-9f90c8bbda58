import Link from "next/link";
import { useTranslations } from 'next-intl';

export function Footer() {
  const t = useTranslations('footer');
  
  return (
    <footer className="border-t border-white/10 bg-indigo-950/50 py-12">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 md:flex-row md:space-x-8 md:space-y-0">
          <a href="#" className="text-sm text-zinc-400 hover:text-white">
            {t('features')}
          </a>
          <a href="#" className="text-sm text-zinc-400 hover:text-white">
            {t('pricing')}
          </a>
          <a href="#" className="text-sm text-zinc-400 hover:text-white">
            {t('about')}
          </a>
          <a href="#" className="text-sm text-zinc-400 hover:text-white">
            {t('contact')}
          </a>
          <a href="#" className="text-sm text-zinc-400 hover:text-white">
            {t('privacy')}
          </a>
        </div>
        <div className="mt-8 border-t border-white/10 pt-6 text-center">
          <p className="text-sm text-zinc-400">
            &copy; {new Date().getFullYear()} Flux-Pix. {t('copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
