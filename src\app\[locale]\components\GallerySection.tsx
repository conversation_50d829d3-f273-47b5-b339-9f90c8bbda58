"use client";

import { X, <PERSON><PERSON><PERSON> } from "lucide-react";
import * as motion from "motion/react-client";
import { useRef, useState, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Masonry from "react-masonry-css";

interface ImagePreviewProps {
  url: string;
  onClose: () => void;
}

function ImagePreview({ url, onClose }: ImagePreviewProps) {
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    },
    [onClose],
  );

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleKeyDown]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/90"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="relative flex h-full max-h-[80vh] w-full max-w-5xl items-center justify-center"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", damping: 25 }}
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute right-4 top-4 z-10 rounded-full bg-black/40 p-1 text-white transition-colors hover:bg-black/60"
          onClick={onClose}
        >
          <X className="h-6 w-6" />
        </button>

        {/* Image only */}
        <motion.div
          className="flex h-full w-full items-center justify-center p-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div
            className={`relative flex aspect-auto max-h-full items-center justify-center rounded-xl shadow-2xl`}
          >
            <Image
              fill
              alt=""
              src={url}
              className="object-contain"
              sizes="(max-width: 1200px) 100vw, 1200px"
            />
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

export function GallerySection() {
  const t = useTranslations("gallery");
  const containerRef = useRef<HTMLDivElement>(null);
  const [previewItem, setPreviewItem] = useState<string | null>(null);

  const breakpointColumnsObj = {
    default: 4,
    1100: 3,
    700: 2,
    500: 1,
  };

  // 编辑案例数据
  const editingCases = [
    {
      before: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop",
      after: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&sat=-20",
      title: "去除水印"
    },
    {
      before: "https://images.unsplash.com/photo-1494790108755-2616c9c0e8e5?w=400&h=300&fit=crop",
      after: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop",
      title: "背景替换"
    },
    {
      before: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop",
      after: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop&sat=-30",
      title: "物体移除"
    },
    {
      before: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop",
      after: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop&brightness=10",
      title: "人像美化"
    }
  ];

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t("sectionTitle")}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t("sectionSubtitle")}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
        {editingCases.map((editingCase, index) => (
          <motion.div
            key={index}
            className="group relative overflow-hidden rounded-xl bg-white/5 backdrop-blur-sm"
            whileHover={{ scale: 1.02 }}
          >
            <div className="relative aspect-[4/3] overflow-hidden">
              {/* Before Image */}
              <div className="absolute inset-0 transition-opacity duration-500 group-hover:opacity-0">
                <Image
                  src={editingCase.before}
                  alt={`${editingCase.title} - ${t("beforeAfter.before")}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                />
                <div className="absolute bottom-2 left-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
                  {t("beforeAfter.before")}
                </div>
              </div>

              {/* After Image */}
              <div className="absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100">
                <Image
                  src={editingCase.after}
                  alt={`${editingCase.title} - ${t("beforeAfter.after")}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                />
                <div className="absolute bottom-2 left-2 rounded bg-violet-600/90 px-2 py-1 text-xs text-white">
                  {t("beforeAfter.after")}
                </div>
              </div>

              {/* Hover Hint */}
              <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                <div className="rounded-lg bg-white/90 px-3 py-2 text-sm font-medium text-gray-900">
                  {t("beforeAfter.hoverToCompare")}
                </div>
              </div>
            </div>

            <div className="p-4">
              <h3 className="font-semibold text-white">{editingCase.title}</h3>
            </div>
          </motion.div>
        ))}
      </div>



      <div className="text-center">
        <Button
          className="bg-gradient-to-r from-violet-600 to-indigo-600 shadow-lg shadow-indigo-500/25 transition-all duration-300 hover:from-violet-500 hover:to-indigo-500"
          size="lg"
        >
          {t("viewMoreButton")} <ArrowRight className="ml-2" />
        </Button>
      </div>
    </section>
  );
}
