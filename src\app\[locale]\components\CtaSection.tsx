import { ArrowR<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';
import { Link } from "@/i18n/routing";

export function CtaSection() {
  const t = useTranslations('cta');
  
  return (
    <section className="py-20">
      <ScrollAnimation
        direction="up"
        className="rounded-2xl border border-violet-500/20 bg-gradient-to-br from-violet-500/10 to-indigo-600/10 p-8 text-center md:p-12"
      >
        <h2 className="mb-6 text-3xl font-bold text-white md:text-4xl">
          {t('title')}
        </h2>
        <p className="mx-auto mb-8 max-w-2xl text-xl text-zinc-300">
          {t('subtitle')}
        </p>

        <div className="mx-auto mt-8 flex max-w-xs flex-col justify-center gap-4 sm:max-w-none sm:flex-row">
          <Button
            size="lg"
            className="bg-gradient-to-r from-violet-600 to-indigo-600 shadow-lg shadow-indigo-500/25 transition-all duration-300 hover:from-violet-500 hover:to-indigo-500"
            asChild
          >
            <Link href="/generate">
              {t('signupButton')} <ArrowRight className="ml-2" />
            </Link>
          </Button>

          <Button
            size="lg"
            variant="outline"
            className="border-violet-500/50 bg-transparent text-white hover:bg-violet-500/10"
          >
            {t('learnMoreButton')}
          </Button>
        </div>

        <p className="mt-6 text-sm text-zinc-400">
          {t('loginHint')}
        </p>
      </ScrollAnimation>
    </section>
  );
}
