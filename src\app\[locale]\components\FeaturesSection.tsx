'use client';

import {
  <PERSON>d<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import * as motion from "motion/react-client";
import { ScrollAnimation } from "@/components/ui/scroll-animation";
import { useTranslations } from 'next-intl';
import Image from "next/image";
import { useState } from "react";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  beforeImage: string;
  afterImage: string;
  isPro?: boolean;
}

function FeatureCard({
  icon,
  title,
  description,
  beforeImage,
  afterImage,
  isPro = false,
}: FeatureCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const t = useTranslations('gallery.beforeAfter');

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`rounded-xl overflow-hidden backdrop-blur-sm ${
        isPro
          ? "border border-violet-500/20 bg-gradient-to-br from-violet-500/10 to-indigo-600/10"
          : "bg-white/5"
      }`}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* 图片展示区域 */}
      <div className="relative aspect-[4/3] overflow-hidden">
        {/* Before Image */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            isHovered ? 'opacity-0' : 'opacity-100'
          }`}
        >
          <Image
            src={beforeImage}
            alt={`${title} - ${t('before')}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute bottom-2 left-2 rounded bg-black/70 px-2 py-1 text-xs text-white">
            {t('before')}
          </div>
        </div>

        {/* After Image */}
        <div
          className={`absolute inset-0 transition-opacity duration-500 ${
            isHovered ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <Image
            src={afterImage}
            alt={`${title} - ${t('after')}`}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute bottom-2 left-2 rounded bg-violet-600/90 px-2 py-1 text-xs text-white">
            {t('after')}
          </div>
        </div>

        {/* Hover Hint */}
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
          <div className="rounded-lg bg-white/90 px-3 py-2 text-sm font-medium text-gray-900">
            {t('hoverToCompare')}
          </div>
        </div>
      </div>

      {/* 文字内容区域 */}
      <div className="p-4">
        <div className="flex items-center gap-2 mb-2">
          <div
            className={`rounded-lg p-2 ${isPro ? "bg-violet-500/20" : "bg-indigo-500/20"} w-fit`}
          >
            {icon}
          </div>
          <h3 className="flex items-center gap-2 text-lg font-bold text-white">
            {title}
            {isPro && (
              <span className="rounded-full bg-violet-500/20 px-2 py-0.5 text-xs font-medium text-violet-300">
                PRO
              </span>
            )}
          </h3>
        </div>
        <p className="text-sm text-zinc-300">{description}</p>
      </div>
    </motion.div>
  );
}

export function FeaturesSection() {
  const t = useTranslations('features');

  // 功能对应的示例图片
  const featureExamples = {
    removeWatermark: {
      before: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&auto=format&sat=-20"
    },
    backgroundEdit: {
      before: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&auto=format"
    },
    objectRemoval: {
      before: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1551963831-b3b1ca40c98e?w=400&h=300&fit=crop&auto=format&sat=-30"
    },
    faceRetouch: {
      before: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=300&fit=crop&auto=format&brightness=10"
    },
    styleTransfer: {
      before: "https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=400&h=300&fit=crop&auto=format&sepia=100"
    },
    textReplacement: {
      before: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&auto=format",
      after: "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop&auto=format&contrast=20"
    }
  };

  return (
    <section className="py-16">
      <ScrollAnimation direction="up" className="mb-16 text-center">
        <h2 className="mb-4 text-3xl font-bold text-white">
          {t('sectionTitle')}
        </h2>
        <p className="mx-auto max-w-3xl text-xl text-zinc-400">
          {t('sectionSubtitle')}
        </p>
      </ScrollAnimation>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        <ScrollAnimation
          direction="up"
          delay={0.1}
        >
          <FeatureCard
            icon={<Eraser className="h-6 w-6 text-indigo-400" />}
            title={t('removeWatermark.title')}
            description={t('removeWatermark.description')}
            beforeImage={featureExamples.removeWatermark.before}
            afterImage={featureExamples.removeWatermark.after}
            isPro={true}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.2}
        >
          <FeatureCard
            icon={<Palette className="h-6 w-6 text-indigo-400" />}
            title={t('backgroundEdit.title')}
            description={t('backgroundEdit.description')}
            beforeImage={featureExamples.backgroundEdit.before}
            afterImage={featureExamples.backgroundEdit.after}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.3}
        >
          <FeatureCard
            icon={<Scissors className="h-6 w-6 text-indigo-400" />}
            title={t('objectRemoval.title')}
            description={t('objectRemoval.description')}
            beforeImage={featureExamples.objectRemoval.before}
            afterImage={featureExamples.objectRemoval.after}
            isPro={true}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.4}
        >
          <FeatureCard
            icon={<Sparkles className="h-6 w-6 text-indigo-400" />}
            title={t('faceRetouch.title')}
            description={t('faceRetouch.description')}
            beforeImage={featureExamples.faceRetouch.before}
            afterImage={featureExamples.faceRetouch.after}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.5}
        >
          <FeatureCard
            icon={<Wand2 className="h-6 w-6 text-indigo-400" />}
            title={t('styleTransfer.title')}
            description={t('styleTransfer.description')}
            beforeImage={featureExamples.styleTransfer.before}
            afterImage={featureExamples.styleTransfer.after}
            isPro={true}
          />
        </ScrollAnimation>

        <ScrollAnimation
          direction="up"
          delay={0.6}
        >
          <FeatureCard
            icon={<Type className="h-6 w-6 text-indigo-400" />}
            title={t('textReplacement.title')}
            description={t('textReplacement.description')}
            beforeImage={featureExamples.textReplacement.before}
            afterImage={featureExamples.textReplacement.after}
          />
        </ScrollAnimation>
      </div>
    </section>
  );
}
