{"navbar": {"features": "Features", "pricing": "Pricing", "gallery": "Gallery", "login": "Log in", "signup": "Sign up", "dashboard": "Dashboard", "openMenu": "Open menu", "closeMenu": "Close menu", "logout": "Log out", "loginWithGoogle": "Log in with Google", "generate": "Generate", "profile": "Profile", "signOut": "Sign out", "signInWithGoogle": "Sign in with Google"}, "hero": {"title": "AI Image Editor", "subtitle": "Describe what you want, AI edits your images", "getStartedButton": "Start Editing", "learnMoreButton": "View Examples", "typingText": "Describe the effect you want, AI makes it happen", "typingMainPart": "Describe the effect you want,", "features": {"removeWatermark": {"title": "Smart Watermark Removal", "description": "Remove watermarks and unwanted elements with one click"}, "backgroundChange": {"title": "Background Replacement", "description": "Easily change image backgrounds with any scene description"}, "objectRemoval": {"title": "Object Removal", "description": "Precisely remove any objects and automatically fill backgrounds"}}}, "features": {"sectionTitle": "Editing Features", "sectionSubtitle": "Simple descriptions, easy editing", "removeWatermark": {"title": "Smart Watermark Removal", "description": "Automatically identify and remove watermarks, logos, and unwanted text while maintaining natural image quality"}, "backgroundEdit": {"title": "Background Editing", "description": "Replace backgrounds, remove backgrounds, or add new scenes with any background description"}, "objectRemoval": {"title": "Object Removal", "description": "Precisely remove any objects, people, or elements from images and intelligently fill blank areas"}, "faceRetouch": {"title": "Portrait Enhancement", "description": "Natural portrait enhancement effects, remove blemishes, adjust skin tone, optimize facial features"}, "styleTransfer": {"title": "Style Transfer", "description": "Transform images into different artistic styles like oil painting, watercolor, sketch, etc."}, "textReplacement": {"title": "Text Replacement", "description": "Intelligently identify and replace text content in images while maintaining original fonts and styles"}}, "cta": {"title": "Start Editing Images", "subtitle": "Upload image, describe effect, AI makes it happen", "signupButton": "Get Started", "learnMoreButton": "Learn More", "loginHint": "No registration required"}, "pricing": {"sectionTitle": "Simple and Transparent Pricing", "sectionSubtitle": "Choose the plan that best fits your needs, all plans include core features", "popularLabel": "Most Popular", "freePlan": {"title": "Free", "price": "$0", "description": "Perfect for first-time users and creative exploration", "buttonText": "Get Started", "credits": "10 credits/day", "features": {"dailyCredits": "10 credits per day", "trialPeriod": "14-day trial of all models", "resultStorage": "Results saved for one month", "privateResults": "Cannot save as private", "commercialUse": "No commercial use", "advertisements": "Includes advertisements"}}, "basicPlan": {"title": "Basic", "price": "$9.9/month", "description": "Ideal for individual creators and small projects", "buttonText": "Choose Basic", "credits": "400 credits/month", "features": {"monthlyCredits": "400 credits per month", "noAds": "No advertisements", "longTermStorage": "Long-term result storage", "privateResults": "Private generation results", "commercialUse": "Commercial use", "unlimitedPrompts": "Unlimited smart prompts"}}, "proPlan": {"title": "Pro", "price": "$29.9/month", "description": "For professional creators and commercial use", "buttonText": "Choose Pro", "credits": "1300 credits/month", "features": {"monthlyCredits": "1300 credits per month", "unlimitedPrompts": "Unlimited smart prompts", "noAds": "No advertisements", "longTermStorage": "Long-term result storage", "privateResults": "Private generation results", "commercialUse": "Commercial use"}}}, "models": {"sectionTitle": "Powerful Flux Model Series", "sectionSubtitle": "From basic creation to professional image generation, the Flux model series meets various creative needs", "proLabel": "PRO", "ultraLabel": "ULTRA", "fluxSchnell": {"title": "Flux Schnell", "description": "Quickly generate basic images, suitable for proof of concept and creative exploration"}, "fluxDev": {"title": "Flux Dev", "description": "Developer-friendly model, supporting basic image generation and simple editing"}, "fluxPro": {"title": "Flux 1.1 Pro", "description": "Faster, better professional image generation, supporting high-quality creation"}, "fluxUltra": {"title": "Flux 1.1 Ultra", "description": "Ultra-high resolution and realism, suitable for professional creation and commercial use"}, "fluxFill": {"title": "Flux Fill", "description": "Powerful image editing and expansion features, supporting precise modifications and content filling"}, "fluxDepthCanny": {"title": "Flux Depth/Canny", "description": "Depth map extraction and edge training, supporting precise control of the image generation process"}}, "gallery": {"sectionTitle": "Editing Examples", "sectionSubtitle": "See real editing results", "viewMoreButton": "View More", "beforeAfter": {"before": "Before", "after": "After", "hoverToCompare": "Hover to compare"}}, "footer": {"product": "Product", "features": "Features", "pricing": "Pricing", "gallery": "Gallery", "resources": "Resources", "documentation": "Documentation", "tutorials": "Tutorials", "blog": "Blog", "company": "Company", "about": "About", "careers": "Careers", "contact": "Contact", "legal": "Legal", "terms": "Terms of Service", "privacy": "Privacy Policy", "cookies": "<PERSON><PERSON>", "copyright": "All rights reserved."}, "locale": {"switchLanguage": "Switch language", "languages": {"zh": "Chinese", "en": "English"}}, "generate": {"title": "Flux Image Generation", "textToImage": "Text to Image", "textToImageShort": "Text", "imageToImage": "Image Editing", "imageToImageShort": "Edit", "prompt": "Prompt", "promptDescription": "Describe the image you want to generate", "clickToChangeImage": "Click to change image", "dragAndDrop": "Drag and drop an image here or click to upload", "supportedFormats": "Supports JPG, PNG formats", "textToImagePlaceholder": "Enter a detailed image description, e.g.: A cute <PERSON><PERSON> Inu sitting on grass, sunny day, high-quality photography style...", "imageToImagePlaceholder": "Describe the modifications you want to make to the image, e.g.: Change the background to a beach scene, add sunset effects...", "smartOptimize": "Smart Optimize", "optimizing": "Optimizing...", "negativePrompt": "Negative Prompt", "negativePromptPlaceholder": "Enter elements you don't want in the image, e.g.: blur, low quality, distortion, unnatural...", "modelSelection": "Model Selection", "advancedSettings": "Advanced Settings", "hideAdvancedSettings": "Hide Advanced Settings", "generateImage": "Generate Image", "generating": "Generating...", "imageSize": "Image Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "generationControl": "Generation Control", "generationCount": "Generation Count", "sheets": "images", "samplingSteps": "Sampling Steps", "steps": "steps", "guidanceScale": "Guidance Scale", "randomSeed": "Random Seed", "generationResults": "Generation Results", "generatingImages": "Generating your images, please wait...", "imagesWillAppearHere": "Your generated images will appear here", "generatedImageAlt": "Generated image {index}", "uploadedImageAlt": "Uploaded image", "selectModelPlaceholder": "Select model"}, "Generate": {"title": "AI Image Editor", "processing": "Processing...", "generate": "Generate Edit", "features": {"title": "Editing Features", "description": "Select a feature to quickly edit your image", "enhance": {"name": "<PERSON><PERSON>ce", "template": "Enhance the image quality and make it look more professional"}, "retouch": {"name": "Retouch", "template": "Remove blemishes and imperfections from the portrait"}, "background": {"name": "Background", "template": "Change the background to "}, "style": {"name": "Style", "template": "Transform the image using style "}, "resize": {"name": "Resize", "template": "Resize the image while maintaining quality"}}, "prompt": {"label": "Editing Instructions", "placeholder": "Describe how you want to edit the image in detail...", "help": "Be specific about what changes you want to make", "suggestions": {"title": "Quick Suggestions", "hide": "Hide suggestions", "show": "Show suggestions"}}, "advanced": {"title": "Advanced Settings", "quality": "Output Quality", "preserve": "Preserve Original Aspects", "format": "Output Format", "enhance": "<PERSON><PERSON>ce <PERSON>"}, "image": {"title": "Image", "description": "Upload an image to edit", "upload": "Upload Your Image", "formats": "Supports JPG, PNG, WEBP, GIF (max 10MB)", "selectButton": "Select Image", "original": "Original", "result": "Result", "comparison": "Before & After", "single": "Single View"}, "actions": {"download": "Download", "clear": "Clear", "reset": "Reset to Original", "undo": "Undo", "redo": "Redo", "batch": "Batch Mode"}, "status": {"uploading": "Uploading...", "processing": "Processing your image...", "complete": "Processing complete!", "error": "Something went wrong. Please try again."}, "batch": {"title": "Batch Processing", "description": "Process multiple images at once", "limit": "Up to 5 images"}}}